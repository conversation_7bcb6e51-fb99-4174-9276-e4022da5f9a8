<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Podcast App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            max-width: 428px;
            margin: 0 auto;
            min-height: 100vh;
            position: relative;
            padding-bottom: 156px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #4c0099;
        }

        .notification-btn {
            position: relative;
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 4px;
            width: 12px;
            height: 12px;
            background-color: #ff5757;
            border-radius: 6px;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin: 0 32px 24px 32px;
        }

        .podcast-list {
            padding: 0 32px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 32px;
            position: relative;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            flex-shrink: 0;
        }

        .play-icon {
            width: 18px;
            height: 18px;
            fill: #4c0099;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 428px;
            height: 156px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding-bottom: 50px;
        }

        .nav-container {
            width: 364px;
            height: 72px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: relative;
        }

        .nav-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0.5;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-item img {
            width: 32px;
            height: 32px;
        }

        .nav-dot {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="images/logo.svg" alt="NCast Logo" class="logo">
            <span class="logo-text">NCAST</span>
        </div>
        <button class="notification-btn">
            <img src="images/bell-icon.svg" alt="Notifications" class="bell-icon">
            <div class="notification-badge"></div>
        </button>
    </div>

    <h1 class="title">Favourite Podcasts</h1>

    <div class="podcast-list">
        <div class="podcast-item">
            <img src="images/podcast1.png" alt="Sunday Summer - Ep3" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Sunday Summer - Ep3</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">15 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast2.png" alt="Musical Soul - Vol. 1" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 1</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">35 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast3.png" alt="Talk Show - Ep4" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep4</div>
                <div class="podcast-category">Business</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast4.png" alt="Musical Soul - Vol. 2" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Musical Soul - Vol. 2</div>
                <div class="podcast-category">Lifestyle</div>
                <div class="podcast-duration">30 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast5.png" alt="Unravelling The Mind" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Unravelling The Mind</div>
                <div class="podcast-category">Healthy Lifestyle</div>
                <div class="podcast-duration">10 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>

        <div class="podcast-item">
            <img src="images/podcast6.png" alt="Talk Show - Ep8" class="podcast-cover">
            <div class="podcast-info">
                <div class="podcast-title">Talk Show - Ep8</div>
                <div class="podcast-category">Entertainment</div>
                <div class="podcast-duration">20 min</div>
            </div>
            <button class="play-btn">
                <svg class="play-icon" viewBox="0 0 18 18">
                    <polygon points="6,3 15,9 6,15" fill="#4c0099"/>
                </svg>
            </button>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-container">
            <div class="nav-item">
                <img src="images/headphones-icon.svg" alt="Headphones">
            </div>
            <div class="nav-item">
                <img src="images/compass-icon.svg" alt="Compass">
            </div>
            <div class="nav-item active">
                <img src="images/heart-icon.svg" alt="Heart">
            </div>
            <div class="nav-item">
                <img src="images/profile-icon.svg" alt="Profile">
            </div>
            <div class="nav-dot"></div>
        </div>
    </div>
</body>
</html>
